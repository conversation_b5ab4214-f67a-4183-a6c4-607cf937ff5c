"""
Обработчики команд и сообщений для Telegram-бота "sh: Media"
"""

import logging
from aiogram import Router
from aiogram.filters import Command
from aiogram.types import Message, BufferedInputFile
from media_openai import generate_image

logger = logging.getLogger(__name__)

# Создаем роутер для обработчиков
router = Router()


@router.message(Command("img"))
async def cmd_img_generate(message: Message):
    """
    Обработчик команды /img для генерации изображений
    Работает только без вложений (чистая генерация по prompt)
    """
    # Извлекаем prompt из команды
    command_text = message.text or ""
    
    # Убираем команду /img и получаем prompt
    if command_text.startswith("/img"):
        prompt = command_text[4:].strip()
    else:
        prompt = ""
    
    # Проверяем, что prompt не пустой
    if not prompt:
        await message.answer(
            "❌ Пожалуйста, укажите описание изображения после команды.\n"
            "Пример: `/img cat astronaut in space`"
        )
        logger.warning(f"Пользователь {message.from_user.id} отправил пустой prompt")
        return
    
    # Проверяем, что это не ответ на сообщение с изображением (это будет в этапе 3)
    if message.reply_to_message:
        await message.answer(
            "🔧 Редактирование изображений будет доступно в следующей версии.\n"
            "Пока что используйте команду без ответа на сообщение."
        )
        return
    
    # Проверяем, что в сообщении нет вложенных изображений (это будет в этапе 3)
    if message.photo:
        await message.answer(
            "🔧 Редактирование изображений будет доступно в следующей версии.\n"
            "Пока что отправьте команду без прикрепленных изображений."
        )
        return
    
    # Отправляем сообщение о начале генерации
    status_message = await message.answer("🎨 Генерирую изображение, подождите...")
    
    try:
        logger.info(f"Пользователь {message.from_user.id} запросил генерацию: '{prompt}'")
        
        # Генерируем изображение
        image_bytes = await generate_image(prompt)
        
        if image_bytes:
            # Создаем файл для отправки
            image_file = BufferedInputFile(
                file=image_bytes,
                filename=f"generated_image.png"
            )
            
            # Отправляем изображение
            await message.answer_photo(
                photo=image_file,
                caption=f"🎨 Изображение по запросу: {prompt}"
            )
            
            # Удаляем сообщение о статусе
            await status_message.delete()
            
            logger.info(f"Изображение успешно отправлено пользователю {message.from_user.id}")
            
        else:
            # Ошибка генерации
            await status_message.edit_text(
                "❌ Не удалось сгенерировать изображение. Попробуйте позже или измените описание."
            )
            logger.error(f"Не удалось сгенерировать изображение для пользователя {message.from_user.id}")
            
    except Exception as e:
        # Обработка неожиданных ошибок
        await status_message.edit_text(
            "❌ Произошла ошибка при генерации изображения. Попробуйте позже."
        )
        logger.error(f"Неожиданная ошибка при обработке команды /img: {e}")


@router.message()
async def handle_other_messages(message: Message):
    """
    Обработчик всех остальных сообщений
    Пока что просто игнорируем или даем подсказку
    """
    if message.text and not message.text.startswith("/"):
        # Если это обычное текстовое сообщение, даем подсказку
        await message.answer(
            "💡 Для генерации изображения используйте команду:\n"
            "`/img <описание изображения>`\n\n"
            "Например: `/img красивый закат над морем`"
        )
    # Остальные типы сообщений игнорируем
