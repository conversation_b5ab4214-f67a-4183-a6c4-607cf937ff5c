"""
Вспомогательные функции для Telegram-бота "sh: Media"
Содержит утилиты для валидации, обработки изображений и логики выбора режима
"""

import logging
from typing import List, Optional
from aiogram.types import Message, PhotoSize

logger = logging.getLogger(__name__)


def extract_prompt_from_command(text: str, command: str = "/img") -> Optional[str]:
    """
    Извлекает prompt из текста команды
    
    Args:
        text: Полный текст сообщения
        command: Команда для поиска (по умолчанию "/img")
        
    Returns:
        str: Извлеченный prompt или None если не найден
    """
    if not text:
        return None
    
    text = text.strip()
    if text.startswith(command):
        prompt = text[len(command):].strip()
        return prompt if prompt else None
    
    return None


def validate_image_size(file_size: int, max_size_mb: int = 20) -> bool:
    """
    Проверяет размер изображения
    
    Args:
        file_size: Размер файла в байтах
        max_size_mb: Максимальный размер в мегабайтах
        
    Returns:
        bool: True если размер допустимый
    """
    max_size_bytes = max_size_mb * 1024 * 1024
    return file_size <= max_size_bytes


async def extract_images_from_message(message: Message) -> List[bytes]:
    """
    Извлекает все изображения из сообщения или реплая
    Будет реализовано на этапе 3
    
    Args:
        message: Сообщение Telegram
        
    Returns:
        List[bytes]: Список изображений в байтах
    """
    # Заглушка для этапа 3
    logger.info("Функция extract_images_from_message будет реализована на этапе 3")
    return []


def determine_operation_mode(message: Message) -> str:
    """
    Определяет режим работы: генерация или редактирование
    
    Args:
        message: Сообщение Telegram
        
    Returns:
        str: "generate" или "edit"
    """
    # Если есть реплай на сообщение с фото или в текущем сообщении есть фото
    if (message.reply_to_message and message.reply_to_message.photo) or message.photo:
        return "edit"
    else:
        return "generate"


def format_error_message(error_type: str) -> str:
    """
    Форматирует сообщения об ошибках для пользователя
    
    Args:
        error_type: Тип ошибки
        
    Returns:
        str: Отформатированное сообщение
    """
    error_messages = {
        "empty_prompt": "❌ Пожалуйста, укажите описание изображения после команды.\nПример: `/img cat astronaut in space`",
        "api_timeout": "⏱️ Запрос занял слишком много времени. Попробуйте позже.",
        "api_error": "❌ Ошибка API. Попробуйте позже или измените описание.",
        "file_too_large": "📁 Файл слишком большой. Максимальный размер: 20 МБ.",
        "invalid_format": "🖼️ Неподдерживаемый формат файла. Используйте изображения (JPG, PNG).",
        "network_error": "🌐 Проблемы с сетью. Попробуйте позже.",
        "unknown_error": "❌ Произошла неожиданная ошибка. Попробуйте позже."
    }
    
    return error_messages.get(error_type, error_messages["unknown_error"])
