"""
Модуль для работы с OpenAI API
Содержит функции для генерации и редактирования изображений
"""

import logging
import httpx
import asyncio
from typing import Optional
from media_config import OPENAI_API_KEY, OPENAI_MODEL

logger = logging.getLogger(__name__)


async def generate_image(prompt: str) -> Optional[bytes]:
    """
    Генерирует изображение по текстовому описанию через OpenAI API
    
    Args:
        prompt: Текстовое описание для генерации изображения
        
    Returns:
        bytes: Данные изображения в формате PNG или None при ошибке
    """
    if not prompt or not prompt.strip():
        logger.warning("Пустой prompt для генерации изображения")
        return None
    
    # Подготавливаем данные для запроса
    headers = {
        "Authorization": f"Bearer {OPENAI_API_KEY}",
        "Content-Type": "application/json"
    }
    
    payload = {
        "model": OPENAI_MODEL,
        "prompt": prompt.strip(),
        "n": 1,
        "size": "1024x1024",
        "response_format": "b64_json"
    }
    
    try:
        async with httpx.AsyncClient(timeout=60.0) as client:
            logger.info(f"Отправляем запрос на генерацию изображения: '{prompt[:50]}...'")
            
            response = await client.post(
                "https://api.openai.com/v1/images/generations",
                headers=headers,
                json=payload
            )
            
            if response.status_code == 200:
                result = response.json()
                
                if "data" in result and len(result["data"]) > 0:
                    import base64
                    image_data = result["data"][0]["b64_json"]
                    image_bytes = base64.b64decode(image_data)
                    
                    logger.info(f"Изображение успешно сгенерировано, размер: {len(image_bytes)} байт")
                    return image_bytes
                else:
                    logger.error("Некорректный ответ от OpenAI API: отсутствуют данные изображения")
                    return None
            else:
                logger.error(f"Ошибка OpenAI API: {response.status_code} - {response.text}")
                return None
                
    except httpx.TimeoutException:
        logger.error("Таймаут при запросе к OpenAI API")
        return None
    except httpx.RequestError as e:
        logger.error(f"Ошибка сети при запросе к OpenAI API: {e}")
        return None
    except Exception as e:
        logger.error(f"Неожиданная ошибка при генерации изображения: {e}")
        return None


async def edit_images(prompt: str, images: list[bytes]) -> Optional[bytes]:
    """
    Редактирует изображения по текстовому описанию через OpenAI API
    
    Args:
        prompt: Текстовое описание изменений
        images: Список изображений в байтах для редактирования
        
    Returns:
        bytes: Данные отредактированного изображения или None при ошибке
    """
    # Заглушка для этапа 3
    logger.info("Функция edit_images будет реализована на этапе 3")
    return None
